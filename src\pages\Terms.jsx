import React from "react";
import { FileText, Scale, AlertTriangle, CheckCircle, DollarSign, Shield } from "lucide-react";

export default function Terms() {
  const sections = [
    {
      icon: CheckCircle,
      title: "ACCEPTANCE OF TERMS",
      content: [
        "By accessing and using GrowthLab SMMA's website and services, you accept and agree to be bound by these Terms of Service. If you do not agree to these terms, you may not use our services.",
        "These terms apply to all visitors, users, and clients who access or use our services, including social media marketing, content creation, advertising management, and related services."
      ]
    },
    {
      icon: FileText,
      title: "SERVICES DESCRIPTION",
      content: [
        "GrowthLab SMMA provides social media marketing services including but not limited to: content creation, social media management, paid advertising campaigns, analytics and reporting, and strategic consulting.",
        "All services are provided according to the specific terms outlined in individual service agreements or proposals.",
        "We reserve the right to modify, suspend, or discontinue any service at any time with reasonable notice."
      ]
    },
    {
      icon: DollarSign,
      title: "PAYMENT TERMS",
      content: [
        "Payment is due according to the schedule outlined in your service agreement, typically monthly in advance.",
        "Setup fees, if applicable, are due upon signing the service agreement.",
        "Late payments may result in service suspension and additional fees.",
        "Refunds are provided according to our 30-day money-back guarantee policy for new clients only."
      ]
    },
    {
      icon: Scale,
      title: "CLIENT RESPONSIBILITIES",
      content: [
        "Provide timely access to necessary accounts, assets, and information required for service delivery.",
        "Ensure all content and materials provided comply with platform policies and applicable laws.",
        "Respond to requests for feedback and approvals within agreed timeframes.",
        "Maintain the confidentiality of account access credentials and notify us immediately of any security concerns."
      ]
    },
    {
      icon: Shield,
      title: "INTELLECTUAL PROPERTY",
      content: [
        "All content created by GrowthLab SMMA becomes the property of the client upon full payment.",
        "Clients grant us permission to use created content in our portfolio and marketing materials unless otherwise specified.",
        "We retain ownership of our proprietary methods, strategies, and business processes.",
        "Clients are responsible for ensuring they have rights to any materials they provide to us."
      ]
    },
    {
      icon: AlertTriangle,
      title: "LIMITATIONS AND DISCLAIMERS",
      content: [
        "While we strive for excellent results, we cannot guarantee specific outcomes such as follower growth, engagement rates, or sales figures.",
        "Social media platforms may change their algorithms or policies, which may affect campaign performance.",
        "We are not liable for any indirect, incidental, or consequential damages arising from our services.",
        "Our total liability is limited to the amount paid for services in the preceding 12 months."
      ]
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className="py-20 bg-green-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">TERMS OF SERVICE</h1>
          </div>
          <p className="text-xl font-bold text-black max-w-2xl mx-auto">
            The legal agreement between GrowthLab SMMA and our clients. 
            Please read these terms carefully before using our services.
          </p>
        </div>
      </section>

      {/* Last Updated */}
      <section className="py-8 bg-yellow-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white px-6 py-3 brutal-border brutal-shadow inline-block">
            <p className="brutal-text">LAST UPDATED: DECEMBER 2024</p>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
            {sections.map((section, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow p-8 ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-pink-500 w-12 h-12 brutal-border brutal-shadow flex items-center justify-center">
                    <section.icon className="w-6 h-6 text-white" />
                  </div>
                  <h2 className="brutal-text text-3xl">{section.title}</h2>
                </div>
                <div className="font-bold text-gray-800 space-y-4">
                  {section.content.map((paragraph, pIndex) => (
                    <p key={pIndex}>{paragraph}</p>
                  ))}
                </div>
              </div>
            ))}

            {/* Termination */}
            <div className="bg-red-500 text-white p-8 brutal-border brutal-shadow">
              <h2 className="brutal-text text-3xl mb-6">TERMINATION</h2>
              <div className="space-y-4 font-bold">
                <p>
                  Either party may terminate services with 30 days written notice. 
                  We reserve the right to terminate services immediately for breach of these terms.
                </p>
                <p>
                  Upon termination, you will receive all completed work and access credentials. 
                  Any outstanding payments remain due.
                </p>
              </div>
            </div>

            {/* Governing Law */}
            <div className="bg-blue-500 text-white p-8 brutal-border brutal-shadow">
              <h2 className="brutal-text text-3xl mb-6">GOVERNING LAW</h2>
              <p className="font-bold">
                These terms are governed by the laws of [Your State/Country]. 
                Any disputes will be resolved through binding arbitration in [Your City, State].
              </p>
            </div>

            {/* Contact */}
            <div className="bg-gray-100 brutal-border p-8 text-center">
              <h3 className="brutal-text text-2xl mb-4">QUESTIONS ABOUT THESE TERMS?</h3>
              <p className="font-bold text-gray-800 mb-4">
                If you have any questions about these Terms of Service, please contact us:
              </p>
              <div className="space-y-2 font-bold text-gray-800">
                <p>Email: <EMAIL></p>
                <p>Phone: +****************</p>
                <p>Address: 123 Digital Street, Suite 100, Marketing City, MC 12345</p>
              </div>
            </div>

            {/* Updates */}
            <div className="bg-yellow-100 brutal-border p-8 text-center">
              <h3 className="brutal-text text-2xl mb-4">TERMS UPDATES</h3>
              <p className="font-bold text-gray-800">
                We reserve the right to modify these terms at any time. 
                Changes will be effective immediately upon posting. 
                Continued use of our services constitutes acceptance of modified terms.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
